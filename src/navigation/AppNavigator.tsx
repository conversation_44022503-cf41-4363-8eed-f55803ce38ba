import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from '@react-navigation/stack';
import { RootStackParamList } from "../types/navigationType";


const Stack = createStackNavigator<RootStackParamList>();


export default function AppNavigator(){
    return <NavigationContainer>
        <Stack.Navigator initialRouteName='Home'>
            <Stack.Screen name="Home" component={HomeScreen} />
        </Stack.Navigator>
    </NavigationContainer>

}