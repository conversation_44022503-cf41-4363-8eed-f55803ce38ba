/**
 * Type definitions for the React Native Anime App
 */

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

export interface TokenResponse {
  token: string;
}

export interface AnimeContent {
  thumbNailImage: string;
  mainImage: string;
  userName: string;
  subTitle: string;
  text: string;
  id: number;
  logo: string;
  title: string;
}

export interface ContentResponse {
  content: AnimeContent;
}

export interface AppState {
  isLoading: boolean;
  isAuthenticated: boolean;
  content: AnimeContent | null;
  error: string | null;
}

export interface AuthState {
  token: string | null;
  email: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface ComponentProps {
  children?: React.ReactNode;
  style?: any;
}

export interface ImageProps {
  source: { uri: string };
  style?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
}

export interface ButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
  style?: any;
  textStyle?: any;
}

export interface InputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  style?: any;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
}

