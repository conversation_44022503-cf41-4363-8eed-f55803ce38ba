import { ApiError } from '../services/ApiService';

export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  VALIDATION = 'VALIDATION',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
  TIMEOUT = 'TIMEOUT',
  PERMISSION = 'PERMISSION',
}

export interface AppError {
  type: ErrorType;
  message: string;
  originalError?: Error;
  timestamp: Date;
  context?: string;
  statusCode?: number;
  retryable?: boolean;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: AppError[] = [];

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle and categorize errors
   */
  handleError(error: unknown, context?: string): AppError {
    const appError = this.categorizeError(error, context);
    this.logError(appError);
    return appError;
  }

  /**
   * Categorize errors based on type and content
   */
  private categorizeError(error: unknown, context?: string): AppError {
    const timestamp = new Date();

    // Handle ApiError
    if (error instanceof ApiError) {
      return {
        type: this.getErrorTypeFromStatus(error.status),
        message: this.getUserFriendlyMessage(error.message, error.status),
        originalError: error,
        timestamp,
        context,
        statusCode: error.status,
        retryable: this.isRetryable(error.status),
      };
    }

    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        type: ErrorType.NETWORK,
        message:
          'Network connection failed. Please check your internet connection.',
        originalError: error as Error,
        timestamp,
        context,
        retryable: true,
      };
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return {
        type: ErrorType.TIMEOUT,
        message: 'Request timed out. Please try again.',
        originalError: error,
        timestamp,
        context,
        retryable: true,
      };
    }

    // Handle validation errors
    if (error instanceof Error && error.message.includes('validation')) {
      return {
        type: ErrorType.VALIDATION,
        message: 'Invalid input provided. Please check your data.',
        originalError: error,
        timestamp,
        context,
        retryable: false,
      };
    }

    // Handle generic errors
    if (error instanceof Error) {
      return {
        type: ErrorType.UNKNOWN,
        message: error.message || 'An unexpected error occurred.',
        originalError: error,
        timestamp,
        context,
        retryable: false,
      };
    }

    // Handle unknown error types
    return {
      type: ErrorType.UNKNOWN,
      message: 'An unexpected error occurred.',
      timestamp,
      context,
      retryable: false,
    };
  }

  /**
   * Get error type from HTTP status code
   */
  private getErrorTypeFromStatus(status?: number): ErrorType {
    if (!status) return ErrorType.UNKNOWN;

    if (status >= 400 && status < 500) {
      if (status === 401 || status === 403) {
        return ErrorType.AUTHENTICATION;
      }
      if (status === 400 || status === 422) {
        return ErrorType.VALIDATION;
      }
      return ErrorType.UNKNOWN;
    }

    if (status >= 500) {
      return ErrorType.SERVER;
    }

    return ErrorType.UNKNOWN;
  }

  /**
   * Get user-friendly error messages
   */
  private getUserFriendlyMessage(message: string, status?: number): string {
    if (status === 401) {
      return 'Authentication failed. Please login again.';
    }
    if (status === 403) {
      return "Access denied. You don't have permission to perform this action.";
    }
    if (status === 404) {
      return 'The requested resource was not found.';
    }
    if (status === 429) {
      return 'Too many requests. Please wait a moment and try again.';
    }
    if (status && status >= 500) {
      return 'Server error. Please try again later.';
    }

    // Return original message if no specific mapping
    return message;
  }

  /**
   * Determine if error is retryable
   */
  private isRetryable(status?: number): boolean {
    if (!status) return false;

    // Retryable status codes
    const retryableStatuses = [408, 429, 500, 502, 503, 504];
    return retryableStatuses.includes(status);
  }

  /**
   * Log error for debugging and analytics
   */
  private logError(error: AppError): void {
    // Add to in-memory log
    this.errorLog.push(error);

    // Keep only last 100 errors
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-100);
    }

    // Console logging for development
    if (__DEV__) {
      console.group(`🚨 Error [${error.type}]`);
      console.error('Message:', error.message);
      console.error('Context:', error.context);
      console.error('Timestamp:', error.timestamp.toISOString());
      console.error('Status Code:', error.statusCode);
      console.error('Retryable:', error.retryable);
      console.error('Original Error:', error.originalError);
      console.groupEnd();
    }

    // In production, you might want to send to crash reporting service
    // Example: Crashlytics.recordError(error.originalError);
  }

  /**
   * Get error log for debugging
   */
  getErrorLog(): AppError[] {
    return [...this.errorLog];
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Get retry delay based on error type
   */
  getRetryDelay(error: AppError, attempt: number): number {
    if (!error.retryable) return 0;

    // Exponential backoff with jitter
    const baseDelay = 1000; // 1 second
    const maxDelay = 30000; // 30 seconds
    const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);

    // Add jitter (±25%)
    const jitter = delay * 0.25 * (Math.random() - 0.5);
    return Math.max(0, delay + jitter);
  }
}

export default ErrorHandler.getInstance();
