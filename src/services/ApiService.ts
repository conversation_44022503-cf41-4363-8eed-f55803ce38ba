/**
 * API Service for handling authentication and content fetching
 * Manages token generation and content retrieval from the backend
 */

import { TokenResponse, AnimeContent } from '../types';

const API_BASE_URL_AUTH = 'https://swsut62sse.execute-api.ap-south-1.amazonaws.com/prod';
const API_BASE_URL_CONTENT = 'https://tzab40im77.execute-api.ap-south-1.amazonaws.com/prod';

// Request timeout in milliseconds
const REQUEST_TIMEOUT = 10000;

class ApiService {
  private token: string | null = null;

  /**
   * Create fetch request with timeout
   */
  private async fetchWithTimeout(
    url: string,
    options: RequestInit,
    timeout: number = REQUEST_TIMEOUT
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout', 408, 'TIMEOUT');
      }
      throw error;
    }
  }

  /**
   * Validate email format
   */
  private validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generate authentication token using email
   * @param email - User email address
   * @returns Promise resolving to authentication token
   * @throws ApiError if request fails
   */
  async generateToken(email: string): Promise<string> {
    // Validate email format
    if (!email || !this.validateEmail(email)) {
      throw new ApiError('Invalid email format', 400, 'VALIDATION_ERROR');
    }

    try {
      const response = await this.fetchWithTimeout(`${API_BASE_URL_AUTH}/generateToken`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new ApiError(
          `Authentication failed: ${errorText}`,
          response.status,
          'AUTH_ERROR'
        );
      }

      const data: TokenResponse = await response.json();
      
      if (!data.token) {
        throw new ApiError('Invalid response: missing token', 500, 'INVALID_RESPONSE');
      }

      this.token = data.token;
      return data.token;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Handle network errors
      if (error instanceof TypeError) {
        throw new ApiError('Network error: Please check your internet connection', 0, 'NETWORK_ERROR');
      }
      
      throw new ApiError('Failed to generate authentication token', 500, 'UNKNOWN_ERROR');
    }
  }

  /**
   * Fetch anime content using the stored token
   * @returns Promise resolving to content data
   * @throws ApiError if request fails or no token available
   */
  async getContent(): Promise<AnimeContent> {
    if (!this.token) {
      throw new ApiError('No authentication token available. Please login first.', 401, 'NO_TOKEN');
    }

    try {
      const response = await this.fetchWithTimeout(`${API_BASE_URL_CONTENT}/getContent`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid
          this.clearToken();
          throw new ApiError('Authentication expired. Please login again.', 401, 'TOKEN_EXPIRED');
        }
        
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new ApiError(
          `Failed to fetch content: ${errorText}`,
          response.status,
          'CONTENT_ERROR'
        );
      }

      const data: { content: AnimeContent } = await response.json();
      
      if (!data.content) {
        throw new ApiError('Invalid response: missing content', 500, 'INVALID_RESPONSE');
      }

      // Validate content structure
      this.validateContentStructure(data.content);
      
      return data.content;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Handle network errors
      if (error instanceof TypeError) {
        throw new ApiError('Network error: Please check your internet connection', 0, 'NETWORK_ERROR');
      }
      
      throw new ApiError('Failed to fetch content', 500, 'UNKNOWN_ERROR');
    }
  }

  /**
   * Validate content structure
   */
  private validateContentStructure(content: any): void {
    const requiredFields = ['id', 'title', 'text', 'userName'];
    const missingFields = requiredFields.filter(field => !content[field]);
    
    if (missingFields.length > 0) {
      throw new ApiError(
        `Invalid content structure: missing ${missingFields.join(', ')}`,
        500,
        'INVALID_CONTENT'
      );
    }
  }

  /**
   * Check if user is authenticated
   * @returns Authentication status
   */
  isAuthenticated(): boolean {
    return !!this.token;
  }

  /**
   * Get current token
   * @returns Current authentication token or null
   */
  getToken(): string | null {
    return this.token;
  }

  /**
   * Set token manually (for testing or persistence)
   * @param token - Authentication token to set
   */
  setToken(token: string): void {
    if (!token) {
      throw new ApiError('Invalid token provided', 400, 'VALIDATION_ERROR');
    }
    this.token = token;
  }

  /**
   * Clear stored token (logout)
   */
  clearToken(): void {
    this.token = null;
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.fetchWithTimeout(`${API_BASE_URL_AUTH}/health`, {
        method: 'GET',
      }, 5000);
      return response.ok;
    } catch {
      return false;
    }
  }
}

/**
 * Custom API Error class for better error handling
 */
export class ApiError extends Error {
  public status?: number;
  public code?: string;

  constructor(message: string, status?: number, code?: string) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
  }
}

export default new ApiService();

