/**
 * Unit Tests for ApiService
 * Tests authentication and content fetching functionality
 */

import ApiService, { ApiError } from '../src/services/ApiService';

// Mock fetch globally
global.fetch = jest.fn();

describe('ApiService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    ApiService.clearToken();
  });

  describe('generateToken', () => {
    it('should generate token successfully with valid email', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ token: 'test-token' }),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const token = await ApiService.generateToken('<EMAIL>');

      expect(token).toBe('test-token');
      expect(ApiService.isAuthenticated()).toBe(true);
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/generateToken'),
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: '<EMAIL>' }),
        })
      );
    });

    it('should throw error for invalid email format', async () => {
      await expect(ApiService.generateToken('invalid-email')).rejects.toThrow(
        'Invalid email format'
      );
      expect(fetch).not.toHaveBeenCalled();
    });

    it('should throw error for empty email', async () => {
      await expect(ApiService.generateToken('')).rejects.toThrow(
        'Invalid email format'
      );
      expect(fetch).not.toHaveBeenCalled();
    });

    it('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValue(new TypeError('Network error'));

      await expect(ApiService.generateToken('<EMAIL>')).rejects.toThrow(
        'Network error: Please check your internet connection'
      );
    });

    it('should handle HTTP errors', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        text: jest.fn().mockResolvedValue('Bad Request'),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      await expect(ApiService.generateToken('<EMAIL>')).rejects.toThrow(
        'Authentication failed: Bad Request'
      );
    });

    it('should handle missing token in response', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({}),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      await expect(ApiService.generateToken('<EMAIL>')).rejects.toThrow(
        'Invalid response: missing token'
      );
    });
  });

  describe('getContent', () => {
    beforeEach(() => {
      ApiService.setToken('test-token');
    });

    it('should fetch content successfully', async () => {
      const mockContent = {
        id: 1,
        title: 'Test Anime',
        text: '<p>Test content</p>',
        userName: 'Test User',
        thumbNailImage: 'https://example.com/thumb.jpg',
        mainImage: 'https://example.com/main.jpg',
        logo: 'https://example.com/logo.jpg',
        subTitle: 'Test subtitle',
      };

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ content: mockContent }),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const content = await ApiService.getContent();

      expect(content).toEqual(mockContent);
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/getContent'),
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json',
          },
        })
      );
    });

    it('should throw error when not authenticated', async () => {
      ApiService.clearToken();

      await expect(ApiService.getContent()).rejects.toThrow(
        'No authentication token available. Please login first.'
      );
      expect(fetch).not.toHaveBeenCalled();
    });

    it('should handle 401 unauthorized and clear token', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        text: jest.fn().mockResolvedValue('Unauthorized'),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      await expect(ApiService.getContent()).rejects.toThrow(
        'Authentication expired. Please login again.'
      );
      expect(ApiService.isAuthenticated()).toBe(false);
    });

    it('should handle missing content in response', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({}),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      await expect(ApiService.getContent()).rejects.toThrow(
        'Invalid response: missing content'
      );
    });

    it('should validate content structure', async () => {
      const mockContent = {
        // Missing required fields: id, title, text, userName
        thumbNailImage: 'https://example.com/thumb.jpg',
      };

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ content: mockContent }),
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      await expect(ApiService.getContent()).rejects.toThrow(
        'Invalid content structure: missing id, title, text, userName'
      );
    });

    it('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValue(new TypeError('Network error'));

      await expect(ApiService.getContent()).rejects.toThrow(
        'Network error: Please check your internet connection'
      );
    });
  });

  describe('token management', () => {
    it('should check authentication status correctly', () => {
      expect(ApiService.isAuthenticated()).toBe(false);

      ApiService.setToken('test-token');
      expect(ApiService.isAuthenticated()).toBe(true);

      ApiService.clearToken();
      expect(ApiService.isAuthenticated()).toBe(false);
    });

    it('should get and set token correctly', () => {
      expect(ApiService.getToken()).toBeNull();

      ApiService.setToken('test-token');
      expect(ApiService.getToken()).toBe('test-token');
    });

    it('should throw error for invalid token', () => {
      expect(() => ApiService.setToken('')).toThrow('Invalid token provided');
    });
  });

  describe('timeout handling', () => {
    it('should handle request timeout', async () => {
      // Mock AbortController
      const mockAbortController = {
        abort: jest.fn(),
        signal: {},
      };
      global.AbortController = jest.fn(() => mockAbortController) as any;

      // Mock fetch to throw AbortError
      const abortError = new Error('The operation was aborted');
      abortError.name = 'AbortError';
      (fetch as jest.Mock).mockRejectedValue(abortError);

      await expect(ApiService.generateToken('<EMAIL>')).rejects.toThrow(
        'Request timeout'
      );
    });
  });
});

describe('ApiError', () => {
  it('should create error with message only', () => {
    const error = new ApiError('Test error');
    expect(error.message).toBe('Test error');
    expect(error.name).toBe('ApiError');
    expect(error.status).toBeUndefined();
    expect(error.code).toBeUndefined();
  });

  it('should create error with status and code', () => {
    const error = new ApiError('Test error', 400, 'BAD_REQUEST');
    expect(error.message).toBe('Test error');
    expect(error.status).toBe(400);
    expect(error.code).toBe('BAD_REQUEST');
  });
});

